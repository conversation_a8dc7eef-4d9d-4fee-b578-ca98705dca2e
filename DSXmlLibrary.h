﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "XmlNode.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "DSXmlLibrary.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UXmlLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	UFUNCTION(Category = "XmlFile")
	static void ExportDataToXml();

	static void CollectGroupElement(FXmlNode*& ParentNode, const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, FString RootTag, FString RootMaterialId, const FString& CabinetNo);

	static void AddNextLevelTagS(const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, FXmlNode* ParentNode, const FString& TagName, const FString& RootTag, const FString& RootMaterialId, const FString& CabinetNo);

	static bool IsPointInPolygon(const FVector2D& Point, const TArray<FVector2D>& Polygon);

private:
	static TMap<int32, FString> XmlTagMap;
};
