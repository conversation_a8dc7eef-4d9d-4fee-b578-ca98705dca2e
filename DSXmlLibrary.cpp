#include "DSXmlLibrary.h"

#include "DesignStationFunctionLibrary.h"
#include "XmlFile.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "SubSystems/UI/DSUISubsystem.h"

TMap<int32, FString> UXmlLibrary::XmlTagMap = {
	{0, TEXT("Unit")}, //（柜体映射）
	{1, TEXT("Functor")}, //（功能件映射）,
	{2, TEXT("DoorBoard")}, //（门板映射）,
	{3, TEXT("Hardware")}, //（五金映射）,
	{4, TEXT("Knob")}, //（拉手映射）,
	{5, TEXT("RomePi11ar")}, //（罗马柱映射）,
	{6, TEXT("Line")}, //（顶线映射）（下拖线映射）（踢脚线映射）,
	{7, TEXT("WashBasin")}, //（水槽映射）,
	{8,TEXT("WaterFaucet")}, //（龙头映射）,
	{9, TEXT("Table")}, //（台面映射）,
	{10, TEXT("CabinetBoard")}, //（柜体板映射）,
	{11, TEXT("DoorCode")}, //（门芯映射）,
	{12, TEXT("Accessory")}, //（附件配件映射）,
	{13, TEXT("Buckle")}, //（扣手映射）,
	{14, TEXT("MetalsScheme")} //（五金方案映射）}
};

bool UXmlLibrary::IsPointInPolygon(const FVector2D& Point, const TArray<FVector2D>& Polygon)
{
	int32 Intersections = 0;
	int32 NumVertices = Polygon.Num();

	constexpr float Tolerance = 0.1f;

	for (int32 i = 0; i < NumVertices; ++i)
	{
		FVector2D A = Polygon[i];
		FVector2D B = Polygon[(i + 1) % NumVertices];

		if (Point.Equals(A, Tolerance) || Point.Equals(B, Tolerance))
		{
			return false;
		}

		if ((A.Y > Point.Y) != (B.Y > Point.Y))
		{
			float IntersectX = (B.X - A.X) * (Point.Y - A.Y) / (B.Y - A.Y) + A.X;

			if (Point.X < IntersectX)
			{
				Intersections++;
			}
			else if (FMath::Abs(Point.X - IntersectX) <= Tolerance)
			{
				return true;
			}
		}
	}

	return (Intersections % 2 == 1);
}

void UXmlLibrary::ExportDataToXml()
{
	TMap<UDSBaseModel*, TArray<UDSBaseModel*>> AreasModelMap;

	if (UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area).Num() < 1)
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("请先绘制房间"));
		return;
	}

	for (auto& Room : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area))
	{
		TArray<UDSBaseModel*> CurAreaModels;
		TSharedPtr<FDSHouseAreaProperty> AreaProperty = Room->GetTypedProperty<FDSHouseAreaProperty>();
		TArray<FVector2D> AreaPolygon;
		for (auto& Point : AreaProperty->Points)
		{
			AreaPolygon.Add(FVector2D(Point.X, Point.Y));
		}

		for (auto& CM : UDSMVCSubsystem::GetInstance()->GetAllCustomModels())
		{
			FVector2D Loc = FVector2D(CM->GetPropertySharedPtr()->TransformProperty.Location.X, CM->GetPropertySharedPtr()->TransformProperty.Location.Y);
			if (IsPointInPolygon(Loc, AreaPolygon) && UDSToolLibrary::IsCustomCabinetType(CM->GetModelType()))
			{
				CurAreaModels.Add(CM);
			}
		}

		AreasModelMap.FindOrAdd(Room).Append(CurAreaModels);
	}

	int32 TotalUDSBaseModelCount = 0;
	for (const TPair<UDSBaseModel*, TArray<UDSBaseModel*>>& Pair : AreasModelMap)
	{
		const TArray<UDSBaseModel*>& ModelsArray = Pair.Value;
		TotalUDSBaseModelCount += ModelsArray.Num();
	}

	if (TotalUDSBaseModelCount < 1)
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("没有找到参与报价的商品"));
		return;
	}

	FXmlFile NewXmlFile;
	const FString RootElementName = TEXT("CabinetLayoutQuote");
	FString MinimalXmlString = FString::Printf(TEXT("<%s></%s>"), *RootElementName, *RootElementName);

	if (!NewXmlFile.LoadFile(MinimalXmlString, EConstructMethod::ConstructFromBuffer))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to initialize FXmlFile with minimal root: %s"), *NewXmlFile.GetLastError());
		return;
	}

	FXmlNode* RootNode = NewXmlFile.GetRootNode();
	if (!RootNode)
	{
		UE_LOG(LogTemp, Error, TEXT("RootNode is null after loading minimal XML. This should not happen if LoadFile succeeded."));
		return;
	}

	TArray<FXmlAttribute> RootAttributes;
	RootAttributes.Add(FXmlAttribute(TEXT("value"), TEXT("橱衣柜报价系统")));
	RootNode->SetAttributes(RootAttributes);

	RootNode->AppendChildNode(TEXT("WardrobeQuote"));
	FXmlNode* WardrobeQuoteNode = nullptr;
	if (RootNode->GetChildrenNodes().Num() > 0)
	{
		WardrobeQuoteNode = RootNode->GetChildrenNodes().Last();
		if (WardrobeQuoteNode && WardrobeQuoteNode->GetTag() != TEXT("WardrobeQuote"))
		{
			UE_LOG(LogTemp, Error, TEXT("Retrieved last child from RootNode, but tag is not WardrobeQuote."));
			WardrobeQuoteNode = nullptr;
		}
	}

	if (WardrobeQuoteNode)
	{
		//<WardrobeQuote>
		{
			TArray<FXmlAttribute> WardrobeQuoteAttributes;
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("quoteWay"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("glocalFactor"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("styleName"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("styleGlass"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("styleGYG"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("hingeBrand"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("slidewayBrand"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("DCFCT"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("Version"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("organization"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("deptId"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("organId"), TEXT("")));
			WardrobeQuoteNode->SetAttributes(WardrobeQuoteAttributes);
		}

		//<SchemeInfo>
		{
			WardrobeQuoteNode->AppendChildNode(TEXT("SchemeInfo"));
			FXmlNode* SchemeInfoNode = WardrobeQuoteNode->GetChildrenNodes().Last();
			TArray<FXmlAttribute> SchemeInfoAttributes;
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("schemeName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("userName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("userPhone"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("cityName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("modelName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("buildName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("designer"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("designerName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("designerPhone"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("designerID"), TEXT("")));
			SchemeInfoNode->SetAttributes(SchemeInfoAttributes);
		}

		//<ShoppingInfo>
		{
			WardrobeQuoteNode->AppendChildNode(TEXT("ShoppingInfo"));
			FXmlNode* ShoppingInfoNode = WardrobeQuoteNode->GetChildrenNodes().Last();
			TArray<FXmlAttribute> ShoppingInfoAttributes;
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopName"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopTel"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopUser"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopMobie"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopProvince"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopCity"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopDistrict"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopAddress"), TEXT("")));
			ShoppingInfoNode->SetAttributes(ShoppingInfoAttributes);
		}

		//<Room>
		for (auto& Room : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area))
		{
			TSharedPtr<FDSHouseAreaProperty> AreaProperty = Room->GetTypedProperty<FDSHouseAreaProperty>();
			WardrobeQuoteNode->AppendChildNode(TEXT("Room"));
			FXmlNode* RoomNode = WardrobeQuoteNode->GetChildrenNodes().Last();

			TArray<FXmlAttribute> RoomAttributes;
			RoomAttributes.Add(FXmlAttribute(TEXT("roomName"), AreaProperty->AreaName));
			RoomAttributes.Add(FXmlAttribute(TEXT("roomId"), Room->GetUUID()));
			RoomNode->SetAttributes(RoomAttributes);

			RoomNode->AppendChildNode(TEXT("Group"));
			FXmlNode* GroupNode = RoomNode->GetChildrenNodes().Last();

			//<Group>
			int32 ModelIndex = 1; // Start cabinet numbering from 1
			for (auto& Model : AreasModelMap[Room])
			{
				UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(Model);
				if (!CupboardModel)
				{
					continue;
				}
				const FParameterData* Tags = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("LXYS"));
				});

				const FParameterData* MaterialNode = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("DZCZ"));
				});

				// Generate cabinet number for this model (starting from 1)
				FString CabinetNo = FString::FromInt(ModelIndex);

				FString RootTag = Tags->Data.GetFormattedValue();
				FString RootMaterialId = MaterialNode->Data.GetFormattedValue();

				CollectGroupElement(GroupNode, CupboardModel->GetModelInfoRef().ComponentTreeData, RootTag, RootMaterialId, CabinetNo);
				ModelIndex++;
			}
		}
	}

	FString FilePath = FPaths::ProjectSavedDir() / TEXT("OutputFromData.xml");
	FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*FPaths::GetPath(FilePath));

	if (NewXmlFile.Save(FilePath))
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::ESuccess, TEXT("XML文件已生成!"));
		UE_LOG(LogTemp, Log, TEXT("XML file successfully generated and saved to: %s"), *FilePath);
	}
	else
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("XML文件生成失败!"));
		UE_LOG(LogTemp, Error, TEXT("Failed to save XML file to: %s. Error: %s"), *FilePath, *NewXmlFile.GetLastError());
	}
}

void UXmlLibrary::CollectGroupElement(FXmlNode*& ParentNode, const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, FString RootTag, FString RootMaterialId, const FString& CabinetNo)
{
	//如果组件节点不可见，则不添加到xml文件中（非操作隐藏bhidden）
	if (FCString::Atoi(*ComponentDataItem->ComponentVisibility.GetFormattedValue()) == 0)
	{
		return;
	}

	const FParameterData* BJJD = ComponentDataItem->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
	{
		return InParam.Data.name.Equals(TEXT("BJJD"));
	});

	const FParameterData* Tags = ComponentDataItem->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
	{
		return InParam.Data.name.Equals(TEXT("LXYS"));
	});

	const FParameterData* MaterialNode = ComponentDataItem->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
	{
		return InParam.Data.name.Equals(TEXT("DZCZ"));
	});

	if (BJJD && FCString::Atoi(*BJJD->Data.GetFormattedValue()) == 0)
	{
		// 当BJJD为0时，跳过当前元素，直接处理子元素
		// 子元素将使用当前传递下来的RootTag、RootMaterialId和CabinetNo

		// 更新RootTag和RootMaterialId（如果当前元素有自己的值）
		if (Tags)
		{
			RootTag = Tags->Data.GetFormattedValue();
		}

		if (MaterialNode)
		{
			RootMaterialId = MaterialNode->Data.GetFormattedValue();
		}

		// 直接处理子元素，将它们输出到当前的父级节点中
		for (auto& It : ComponentDataItem->ChildComponent)
		{
			if (FCString::Atoi(*It->ComponentVisibility.GetFormattedValue()) == 0)
			{
				continue;
			}
			// 跳级处理：子元素直接继承当前的CabinetNo，不增加层级
			CollectGroupElement(ParentNode, It, RootTag, RootMaterialId, CabinetNo);
		}
		return; // 跳过当前元素的正常处理流程
	}

	if (!Tags && RootTag.IsEmpty()) //根没有LXYS，且自身也没有LXYS，则不添加到xml文件中
	{
		return;
	}

	if (Tags) //如果有自己的LXYS用自己的，没有用根传递下来的，并且要将RootTag设置为LXYS的值
	{
		RootTag = Tags->Data.GetFormattedValue();
	}

	if (MaterialNode) //如果有自己的DZCZ用自己的，没有用根传递下来的
	{
		RootMaterialId = MaterialNode->Data.GetFormattedValue();
	}

	AddNextLevelTagS(ComponentDataItem, ParentNode, XmlTagMap[FCString::Atoi(*RootTag)], RootTag, RootMaterialId, CabinetNo);
}

//Unit下层开始递归
void UXmlLibrary::AddNextLevelTagS(const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, FXmlNode* ParentNode, const FString& TagName, const FString& RootTag, const FString& RootMaterialId, const FString& CabinetNo)
{
	ParentNode->AppendChildNode(TagName);
	FXmlNode* SelfNode = ParentNode->GetChildrenNodes().Last();

	TMap<FString, FString> SpecialValueMap;
	TMap<FString, FString> MustValueMap;

	MustValueMap.Add(TEXT("NAME"), ComponentDataItem->ComponentName);
	MustValueMap.Add(TEXT("CABINETNO"), CabinetNo);
	MustValueMap.Add(TEXT("UID"), ComponentDataItem->UUID);
	MustValueMap.Add(TEXT("FOLDERID"), ComponentDataItem->ComponentID.GetFormattedValue());
	if (auto ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(RootMaterialId))
	{
		MustValueMap.Add(TEXT("MATERIALNAME"), ResourceInfo->Name);
		MustValueMap.Add(TEXT("MATERIALDESIGNCODE"), ResourceInfo->FolderCode);
	}
	MustValueMap.Add(TEXT("LOCATIONX"), ComponentDataItem->ComponentLocation.LocationX.GetFormattedValue());
	MustValueMap.Add(TEXT("LOCATIONY"), ComponentDataItem->ComponentLocation.LocationY.GetFormattedValue());
	MustValueMap.Add(TEXT("LOCATIONZ"), ComponentDataItem->ComponentLocation.LocationZ.GetFormattedValue());
	MustValueMap.Add(TEXT("ROLL"), ComponentDataItem->ComponentRotation.Roll.GetFormattedValue());
	MustValueMap.Add(TEXT("PITCH"), ComponentDataItem->ComponentRotation.Pitch.GetFormattedValue());
	MustValueMap.Add(TEXT("YAW"), ComponentDataItem->ComponentRotation.Yaw.GetFormattedValue());
	MustValueMap.Add(TEXT("SCALEX"), ComponentDataItem->ComponentScale.X.GetFormattedValue());
	MustValueMap.Add(TEXT("SCALEY"), ComponentDataItem->ComponentScale.Y.GetFormattedValue());
	MustValueMap.Add(TEXT("SCALEZ"), ComponentDataItem->ComponentScale.Z.GetFormattedValue());

	for (auto& It : ComponentDataItem->ComponentParameters)
	{
		if (!It.Data.Special.IsEmpty() && FCString::Atoi(*It.Data.Special_exp) == 1)
		{
			SpecialValueMap.Add(It.Data.name, It.Data.value);
		}

		if (!It.Data.Must.IsEmpty() && FCString::Atoi(*It.Data.Must_exp) == 1)
		{
			MustValueMap.Add(It.Data.name, It.Data.value);
		}
	}

	TArray<FXmlAttribute> SpecialAttributes;
	TArray<FXmlAttribute> MustAttributes;

	for (auto& SA : SpecialValueMap)
	{
		SpecialAttributes.Add(FXmlAttribute(SA.Key, SA.Value));
	}

	for (auto& MA : MustValueMap)
	{
		MustAttributes.Add(FXmlAttribute(MA.Key, MA.Value));
	}

	//先添加自己的Must参数
	SelfNode->SetAttributes(MustAttributes);

	//再添加自己标签下，第一个SpecialVariables标签
	SelfNode->AppendChildNode(TEXT("SpecialVariables"));
	FXmlNode* ChildSpecialNode = SelfNode->GetChildrenNodes().Last();
	ChildSpecialNode->SetAttributes(SpecialAttributes);

	int32 ChildIndex = 1;
	for (auto& It : ComponentDataItem->ChildComponent)
	{
		if (FCString::Atoi(*It->ComponentVisibility.GetFormattedValue()) == 0)
		{
			continue;
		}
		// Generate hierarchical cabinet number for child components
		FString ChildCabinetNo = CabinetNo + TEXT("-") + FString::FromInt(ChildIndex);
		CollectGroupElement(SelfNode, It, RootTag, RootMaterialId, ChildCabinetNo);
		ChildIndex++;
	}
}
